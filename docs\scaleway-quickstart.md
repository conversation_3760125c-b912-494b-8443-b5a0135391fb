# Scaleway Quick Start Guide

## Overview

Simple 3-step setup to use Scaleway Mac mini for all iOS builds instead of expensive GitHub Actions runners.

**Cost Savings**: €10.56/month (weekly builds) vs €24/month (GitHub Actions) = 56% savings

## Step 1: Create Scaleway Mac mini

1. **Sign up**: https://console.scaleway.com/register
2. **Create Mac mini**:
   - Go to **Apple Silicon** → **Mac mini M1**
   - **Zone**: Paris (PAR3)
   - **Type**: Mac mini M1 (€0.11/hour)
   - **Add your SSH key**
   - Click **Create**

**Note**: 24-hour minimum billing starts when created!

## Step 2: Setup Runner

### Connect to your Mac mini
```bash
ssh root@<your-mac-mini-ip>
```

### Run automated setup
```bash
# Download and run setup script
curl -O https://raw.githubusercontent.com/dr-muscle/DrMuscle/main/scripts/setup-scaleway-runner.sh
chmod +x setup-scaleway-runner.sh
./setup-scaleway-runner.sh
```

### Provide required information
When prompted, enter:
- **Repository URL**: `https://github.com/dr-muscle/DrMuscle`
- **Runner Token**: Get from GitHub → Settings → Actions → Runners → New self-hosted runner

## Step 3: Install Xcode

### Connect via VNC (for GUI access)
```bash
# On Mac mini, enable VNC
sudo /System/Library/CoreServices/RemoteManagement/ARDAgent.app/Contents/Resources/kickstart \
  -activate -configure -access -on -clientopts -setvnclegacy -vnclegacy yes \
  -clientopts -setvncpw -vncpw your_password -restart -agent -privs -all
```

### Install Xcode
1. **Connect via VNC** to your Mac mini IP
2. **Open App Store**
3. **Download and install Xcode**
4. **Accept license agreements**

## That's It! 🎉

Your iOS builds will now automatically use the Scaleway runner instead of GitHub Actions runners.

## Usage

### Trigger iOS Build
1. Go to **Actions** → **iOS & Android Build & Deploy**
2. Click **Run workflow**
3. Select **iOS** or **iOS & Android**
4. Your build runs on Scaleway (€2.64 per 24h session)

### Cost Optimization Tips

#### Batch Builds (Recommended)
- **Collect changes** during the week
- **Build once weekly** to maximize 24-hour session value
- **Build multiple variants** in one session

#### Example Weekly Schedule
```
Monday 9 AM: Trigger iOS build
- Main app Debug build
- Main app Release build  
- Upload to TestFlight
- Update documentation
- All within 24-hour session = €2.64
```

## Management Commands

### Check Runner Status
```bash
ssh root@<mac-mini-ip> "~/runner-status.sh"
```

### Start/Stop Runner
```bash
# Start
ssh root@<mac-mini-ip> "~/start-runner.sh"

# Stop  
ssh root@<mac-mini-ip> "~/stop-runner.sh"
```

### Auto-Shutdown
The runner includes an auto-shutdown script that powers down the Mac mini when no jobs are running, helping minimize costs.

## Cost Comparison

| Strategy | Scaleway Cost | GitHub Actions Cost | Savings |
|----------|---------------|-------------------|---------|
| Weekly builds | €10.56/month | €24/month | 56% |
| Bi-weekly builds | €5.28/month | €12/month | 56% |
| Monthly builds | €2.64/month | €6/month | 56% |

## Troubleshooting

### Runner Not Showing Up
```bash
# Check runner service
ssh root@<mac-mini-ip> "sudo ~/actions-runner/svc.sh status"

# Restart if needed
ssh root@<mac-mini-ip> "sudo ~/actions-runner/svc.sh restart"
```

### Build Failures
1. **Verify Xcode installation**
2. **Check code signing certificates** (copy from your local Mac)
3. **Verify provisioning profiles** (stored as GitHub secrets)

### Connection Issues
1. **Check Scaleway firewall** (allow SSH port 22)
2. **Verify SSH key** is correctly added
3. **Test basic connectivity**: `ping <mac-mini-ip>`

## Support

- **Scaleway Docs**: https://www.scaleway.com/en/docs/
- **GitHub Actions**: https://docs.github.com/en/actions
- **Setup Issues**: Check `docs/scaleway-setup.md` for detailed guide

## Summary

✅ **Simple setup**: 3 steps, mostly automated  
✅ **Cost effective**: 56% savings vs GitHub Actions  
✅ **No workflow changes**: Automatically uses Scaleway  
✅ **Flexible**: Can still use GitHub Actions for urgent builds  

Your iOS builds now cost €2.64 per session instead of €2.40 per build! 🚀

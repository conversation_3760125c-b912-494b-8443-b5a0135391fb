# Scaleway Mac mini Setup for iOS Builds

## Overview

This guide configures a Scaleway Mac mini M1 as a GitHub Actions self-hosted runner for iOS builds, optimizing for the 24-hour minimum billing cycle.

## Prerequisites

- Scaleway account
- GitHub repository with admin access
- Basic SSH knowledge

## Step 1: Create Scaleway Mac mini

1. **Login to Scaleway Console**: https://console.scaleway.com/
2. **Navigate**: Apple Silicon → Mac mini M1
3. **Configuration**:
   - **Zone**: Paris (PAR3)
   - **Type**: Mac mini M1 (€0.11/hour)
   - **OS**: macOS Sonoma 14 (default)
   - **SSH Key**: Add your public SSH key
4. **Create** the instance

**Important**: 24-hour minimum billing starts when created!

## Step 2: Initial Mac mini Setup

### Connect via SSH
```bash
ssh root@<your-mac-mini-ip>
```

### Install Required Tools
```bash
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Git and other tools
brew install git

# Install Xcode Command Line Tools
xcode-select --install
```

### Install Xcode (Required for iOS builds)
```bash
# Download Xcode from App Store or Apple Developer Portal
# This requires GUI access via VNC
```

## Step 3: Setup GitHub Actions Self-Hosted Runner

### Generate Runner Token
1. Go to your GitHub repository
2. **Settings** → **Actions** → **Runners**
3. Click **New self-hosted runner**
4. Select **macOS** and **ARM64**
5. Copy the configuration commands

### Configure Runner on Mac mini
```bash
# Create runner directory
mkdir actions-runner && cd actions-runner

# Download runner package
curl -o actions-runner-osx-arm64-2.311.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.311.0/actions-runner-osx-arm64-2.311.0.tar.gz

# Extract
tar xzf ./actions-runner-osx-arm64-2.311.0.tar.gz

# Configure (use token from GitHub)
./config.sh --url https://github.com/dr-muscle/DrMuscle --token <YOUR_TOKEN>

# Install as service
sudo ./svc.sh install
sudo ./svc.sh start
```

### Configure Runner Labels
When configuring, add these labels:
- `scaleway`
- `macos`
- `ios-build`
- `self-hosted`

## Step 4: Optimize for 24-Hour Billing Cycle

### Strategy: Batch Builds
Since you pay for 24 hours minimum, maximize usage:

1. **Accumulate changes** before triggering iOS builds
2. **Build multiple versions** in one session
3. **Use scheduled builds** for non-urgent updates
4. **Manual triggers** for important releases

### Auto-shutdown Script
Create a script to shutdown after builds complete:

```bash
#!/bin/bash
# auto-shutdown.sh
# Place in /usr/local/bin/

# Wait for all GitHub Actions jobs to complete
while pgrep -f "Runner.Worker" > /dev/null; do
    echo "GitHub Actions job running, waiting..."
    sleep 60
done

echo "No active jobs, scheduling shutdown in 30 minutes..."
sleep 1800  # 30 minutes grace period
sudo shutdown -h now
```

## Step 5: Update GitHub Actions Workflow

### Modify Workflow for Scaleway Runner
Add conditional logic to use Scaleway runner for iOS builds:

```yaml
build-ios:
  name: iOS Build (Scaleway Mac mini)
  if: github.event.inputs.platform == 'iOS & Android' || github.event.inputs.platform == 'iOS'
  runs-on: [self-hosted, scaleway, macos]  # Use Scaleway runner
  # ... rest of iOS build steps
```

## Cost Optimization Tips

### 1. Batch Strategy
- **Collect multiple changes** before building
- **Build nightly** instead of per-commit
- **Use for releases** and important milestones

### 2. Monitoring Usage
- Track build frequency
- Monitor 24-hour utilization
- Consider patterns (weekdays vs weekends)

### 3. Alternative Approach: Scheduled Sessions
```yaml
# Weekly iOS build schedule
schedule:
  - cron: '0 9 * * 1'  # Monday 9 AM UTC
```

## Troubleshooting

### Runner Not Appearing
1. Check runner service: `sudo ./svc.sh status`
2. Restart if needed: `sudo ./svc.sh restart`
3. Check logs: `tail -f _diag/Runner_*.log`

### Build Failures
1. Verify Xcode installation
2. Check certificates and provisioning profiles
3. Ensure sufficient disk space

### Network Issues
1. Check Scaleway firewall settings
2. Verify SSH key configuration
3. Test connectivity: `ping github.com`

## Security Considerations

1. **Use SSH keys** instead of passwords
2. **Limit runner access** to specific repositories
3. **Regular updates** of macOS and tools
4. **Monitor runner logs** for suspicious activity

## Cost Tracking

### Expected Costs (24-hour sessions)
- **Per session**: €2.64 (24h × €0.11/h)
- **Weekly builds**: ~€10.56/month
- **Bi-weekly builds**: ~€5.28/month

### Break-even Analysis
- **GitHub Actions**: ~€29/month (10 builds × 30min × €0.08/min)
- **Scaleway weekly**: ~€10.56/month
- **Savings**: ~64% with weekly batching

## Next Steps

1. Create Scaleway Mac mini
2. Configure as self-hosted runner
3. Update GitHub Actions workflow
4. Test with a sample iOS build
5. Implement batching strategy
6. Monitor costs and optimize

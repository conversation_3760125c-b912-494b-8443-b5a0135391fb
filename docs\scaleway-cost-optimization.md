# Scaleway Cost Optimization Guide

## Overview

This guide helps you maximize value from Scaleway Mac mini's 24-hour minimum billing cycle for iOS builds.

## Cost Analysis

### Current GitHub Actions Cost
- **Rate**: ~€0.08/minute for macOS runners
- **Typical build**: 30 minutes = €2.40 per build
- **Monthly estimate** (10 builds): €24.00

### Scaleway Mac mini Cost
- **Rate**: €0.11/hour
- **Minimum**: 24 hours = €2.64 per session
- **Break-even**: 11 hours of GitHub Actions usage per session

## Optimization Strategies

### 1. Batch Building Strategy

#### Weekly Batch Builds
```yaml
# Add to your workflow
schedule:
  - cron: '0 9 * * 1'  # Monday 9 AM UTC
```

**Benefits**:
- 1 session per week = €10.56/month
- 56% savings vs GitHub Actions
- Predictable costs

#### Accumulate-and-Build Pattern
1. **Collect changes** throughout the week
2. **Trigger manual build** when ready
3. **Build multiple variants** in one session:
   - Debug build for testing
   - Release build for TestFlight
   - Different configurations

### 2. Multi-Build Sessions

#### Example Session Plan (within 24 hours)
```bash
# Hour 0-2: Main app build
- iOS Debug build
- iOS Release build
- Upload to TestFlight

# Hour 2-4: Watch app builds (future)
- Watch Debug build
- Watch Release build

# Hour 4-6: Testing & validation
- Run automated tests
- Performance testing
- UI testing

# Hour 6-8: Documentation & cleanup
- Generate documentation
- Update version numbers
- Cleanup artifacts
```

### 3. Scheduled Optimization

#### Smart Scheduling
```yaml
# Different schedules for different needs
on:
  schedule:
    # Weekly release builds
    - cron: '0 9 * * 1'    # Monday 9 AM
    # Bi-weekly beta builds  
    - cron: '0 9 * * 1,3'  # Monday & Wednesday
    # Monthly major builds
    - cron: '0 9 1 * *'    # 1st of month
  
  workflow_dispatch:
    inputs:
      build_type:
        type: choice
        options:
          - 'Quick Test Build'
          - 'Full Release Build'
          - 'Multi-Variant Build'
```

### 4. Session Management

#### Pre-Session Checklist
- [ ] Accumulate all pending changes
- [ ] Prepare multiple build configurations
- [ ] Queue testing tasks
- [ ] Plan documentation updates

#### During Session
- [ ] Monitor build progress
- [ ] Run additional tasks while building
- [ ] Prepare next session's work
- [ ] Update project documentation

#### Post-Session
- [ ] Verify all builds completed
- [ ] Download artifacts
- [ ] Schedule next session
- [ ] Document any issues

## Cost Comparison Scenarios

### Scenario 1: Weekly Builds
- **Frequency**: 1 session per week
- **Scaleway Cost**: €10.56/month
- **GitHub Actions Equivalent**: €24.00/month
- **Savings**: 56%

### Scenario 2: Bi-weekly Builds
- **Frequency**: 2 sessions per month
- **Scaleway Cost**: €5.28/month
- **GitHub Actions Equivalent**: €12.00/month
- **Savings**: 56%

### Scenario 3: Emergency Builds
- **Use GitHub Actions** for urgent fixes
- **Use Scaleway** for planned releases
- **Hybrid approach** for optimal cost/speed balance

## Automation Scripts

### Session Planner Script
```bash
#!/bin/bash
# session-planner.sh

echo "Scaleway Build Session Planner"
echo "=============================="

# Check pending changes
git fetch origin
CHANGES=$(git log --oneline HEAD..origin/main | wc -l)
echo "Pending changes: $CHANGES"

# Estimate session value
if [ $CHANGES -gt 5 ]; then
    echo "✅ Good session value - many changes to build"
elif [ $CHANGES -gt 2 ]; then
    echo "⚠️  Moderate session value - consider waiting"
else
    echo "❌ Low session value - wait for more changes"
fi

# Suggest build types
echo ""
echo "Suggested builds for this session:"
echo "- Main iOS app (Debug + Release)"
echo "- TestFlight upload"
if [ -d "DrMuscleWatch" ]; then
    echo "- Watch app builds"
fi
echo "- Documentation updates"
echo "- Version bumps"
```

### Cost Tracker Script
```bash
#!/bin/bash
# cost-tracker.sh

SESSIONS_FILE="$HOME/.scaleway-sessions"
COST_PER_SESSION=2.64

# Log new session
log_session() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'),session_start" >> "$SESSIONS_FILE"
}

# Calculate monthly cost
monthly_cost() {
    CURRENT_MONTH=$(date '+%Y-%m')
    SESSIONS=$(grep "$CURRENT_MONTH" "$SESSIONS_FILE" | wc -l)
    COST=$(echo "$SESSIONS * $COST_PER_SESSION" | bc)
    echo "This month: $SESSIONS sessions = €$COST"
}

case "$1" in
    "start")
        log_session
        echo "Session logged. Remember: 24-hour minimum billing!"
        ;;
    "cost")
        monthly_cost
        ;;
    *)
        echo "Usage: $0 {start|cost}"
        ;;
esac
```

## Monitoring & Alerts

### Build Duration Tracking
```yaml
# Add to workflow
- name: Track Session Utilization
  run: |
    echo "Session started at: $(date)"
    echo "Estimated completion: $(date -d '+4 hours')"
    echo "Session expires at: $(date -d '+24 hours')"
```

### Slack Notifications
```yaml
- name: Notify Session Status
  if: always()
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
    text: |
      Scaleway Session Update:
      • Build: ${{ job.status }}
      • Duration: ${{ steps.build.outputs.duration }}
      • Remaining: ${{ steps.calculate.outputs.remaining_hours }}h
      • Cost: €2.64 (24h minimum)
```

## Best Practices

### 1. Session Planning
- **Plan ahead**: Schedule sessions when you have multiple tasks
- **Batch work**: Combine builds, tests, and documentation
- **Use calendars**: Set reminders for optimal build times

### 2. Emergency Handling
- **Keep GitHub Actions** for urgent hotfixes
- **Use Scaleway** for planned releases and features
- **Document decision criteria** for runner selection

### 3. Team Coordination
- **Communicate sessions**: Let team know when Scaleway is active
- **Queue requests**: Collect build requests for next session
- **Share costs**: Track and report cost savings

## ROI Analysis

### Break-even Calculation
- **Scaleway session**: €2.64 (24 hours)
- **GitHub Actions equivalent**: €2.64 ÷ €0.08 = 33 minutes
- **Break-even**: Any session using >33 minutes saves money

### Annual Savings Projection
- **Current cost**: €288/year (€24/month × 12)
- **Scaleway weekly**: €126.72/year (€10.56/month × 12)
- **Annual savings**: €161.28 (56% reduction)

## Conclusion

With proper planning and batching, Scaleway Mac mini can provide significant cost savings while maintaining build quality. The key is maximizing the value of each 24-hour session through strategic planning and automation.

#!/bin/bash

# Scaleway Mac mini GitHub Actions Runner Setup Script
# Run this script on your Scaleway Mac mini to configure it as a self-hosted runner

set -e

echo "🚀 Setting up Scaleway Mac mini as GitHub Actions Runner"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script must be run on macOS (Scaleway Mac mini)"
    exit 1
fi

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root. Some operations may need to be run as regular user."
fi

print_status "Step 1: Installing Homebrew and dependencies"

# Install Homebrew if not present
if ! command -v brew &> /dev/null; then
    echo "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
else
    print_status "Homebrew already installed"
fi

# Install required tools
echo "Installing Git and other dependencies..."
brew install git curl

print_status "Step 2: Installing Xcode Command Line Tools"

# Install Xcode Command Line Tools
if ! xcode-select -p &> /dev/null; then
    echo "Installing Xcode Command Line Tools..."
    xcode-select --install
    
    print_warning "Please complete the Xcode Command Line Tools installation in the GUI"
    print_warning "Press Enter when installation is complete..."
    read -r
else
    print_status "Xcode Command Line Tools already installed"
fi

print_status "Step 3: Setting up GitHub Actions Runner"

# Get repository and token from user
echo ""
echo "Please provide the following information:"
read -p "GitHub Repository URL (e.g., https://github.com/dr-muscle/DrMuscle): " REPO_URL
read -p "GitHub Runner Token (from Settings > Actions > Runners > New self-hosted runner): " RUNNER_TOKEN

if [[ -z "$REPO_URL" || -z "$RUNNER_TOKEN" ]]; then
    print_error "Repository URL and Runner Token are required"
    exit 1
fi

# Create runner directory
RUNNER_DIR="$HOME/actions-runner"
mkdir -p "$RUNNER_DIR"
cd "$RUNNER_DIR"

# Download GitHub Actions Runner
RUNNER_VERSION="2.311.0"
RUNNER_FILE="actions-runner-osx-arm64-${RUNNER_VERSION}.tar.gz"

if [[ ! -f "$RUNNER_FILE" ]]; then
    echo "Downloading GitHub Actions Runner..."
    curl -o "$RUNNER_FILE" -L "https://github.com/actions/runner/releases/download/v${RUNNER_VERSION}/${RUNNER_FILE}"
    
    # Extract runner
    tar xzf "$RUNNER_FILE"
else
    print_status "GitHub Actions Runner already downloaded"
fi

# Configure runner
echo "Configuring GitHub Actions Runner..."
./config.sh --url "$REPO_URL" --token "$RUNNER_TOKEN" --labels "scaleway,macos,ios-build,self-hosted" --name "scaleway-mac-mini-$(hostname)" --work "_work"

print_status "Step 4: Installing runner as a service"

# Install and start runner service
sudo ./svc.sh install
sudo ./svc.sh start

print_status "Step 5: Creating auto-shutdown script"

# Create auto-shutdown script
SHUTDOWN_SCRIPT="/usr/local/bin/auto-shutdown.sh"
sudo tee "$SHUTDOWN_SCRIPT" > /dev/null << 'EOF'
#!/bin/bash
# Auto-shutdown script for Scaleway Mac mini
# Shuts down the machine when no GitHub Actions jobs are running

LOG_FILE="/var/log/auto-shutdown.log"

log_message() {
    echo "$(date): $1" >> "$LOG_FILE"
}

log_message "Auto-shutdown script started"

# Wait for all GitHub Actions jobs to complete
while pgrep -f "Runner.Worker" > /dev/null; do
    log_message "GitHub Actions job running, waiting..."
    sleep 60
done

log_message "No active jobs detected"

# Additional grace period
GRACE_PERIOD=1800  # 30 minutes
log_message "Waiting ${GRACE_PERIOD} seconds grace period before shutdown..."
sleep $GRACE_PERIOD

# Final check
if pgrep -f "Runner.Worker" > /dev/null; then
    log_message "New job detected during grace period, aborting shutdown"
    exit 0
fi

log_message "Initiating shutdown"
sudo shutdown -h now
EOF

# Make script executable
sudo chmod +x "$SHUTDOWN_SCRIPT"

print_status "Step 6: Creating management scripts"

# Create start script
tee "$HOME/start-runner.sh" > /dev/null << 'EOF'
#!/bin/bash
echo "Starting GitHub Actions Runner service..."
sudo /Users/<USER>/actions-runner/svc.sh start
echo "Runner service started"
EOF

# Create stop script
tee "$HOME/stop-runner.sh" > /dev/null << 'EOF'
#!/bin/bash
echo "Stopping GitHub Actions Runner service..."
sudo /Users/<USER>/actions-runner/svc.sh stop
echo "Runner service stopped"
EOF

# Create status script
tee "$HOME/runner-status.sh" > /dev/null << 'EOF'
#!/bin/bash
echo "GitHub Actions Runner Status:"
sudo /Users/<USER>/actions-runner/svc.sh status
echo ""
echo "Active Runner Processes:"
pgrep -f "Runner" | wc -l | xargs echo "Runner processes:"
echo ""
echo "Recent logs:"
tail -n 10 /Users/<USER>/actions-runner/_diag/Runner_*.log 2>/dev/null || echo "No logs found"
EOF

# Make scripts executable
chmod +x "$HOME/start-runner.sh"
chmod +x "$HOME/stop-runner.sh"
chmod +x "$HOME/runner-status.sh"

print_status "Setup Complete!"

echo ""
echo "🎉 Scaleway Mac mini is now configured as a GitHub Actions Runner!"
echo ""
echo "📋 Summary:"
echo "   • Runner Name: scaleway-mac-mini-$(hostname)"
echo "   • Labels: scaleway, macos, ios-build, self-hosted"
echo "   • Service Status: Running"
echo ""
echo "🛠️  Management Commands:"
echo "   • Check status: ~/runner-status.sh"
echo "   • Start runner: ~/start-runner.sh"
echo "   • Stop runner: ~/stop-runner.sh"
echo "   • Auto-shutdown: $SHUTDOWN_SCRIPT"
echo ""
echo "⚠️  Important Notes:"
echo "   • Install Xcode from App Store for iOS builds"
echo "   • Configure code signing certificates"
echo "   • Set up provisioning profiles"
echo "   • Remember: 24-hour minimum billing on Scaleway"
echo ""
echo "🔗 Next Steps:"
echo "   1. Install Xcode (requires GUI/VNC access)"
echo "   2. Test the runner with a sample workflow"
echo "   3. Configure your GitHub Actions workflow to use 'Scaleway (Cost-effective)' option"
echo ""

# Check runner status
print_status "Checking runner status..."
sudo ./svc.sh status

echo ""
print_status "Runner setup completed successfully! 🚀"
